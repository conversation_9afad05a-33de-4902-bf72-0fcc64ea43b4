import logging, uuid, asyncio, redis

from livekit.agents import JobContext, WorkerOptions, cli, RoomInputOptions
from livekit.agents.voice import AgentSession
from livekit.rtc import participant
from config.logging import setup_logging
from livekit_handlers.recording import RoomRecodingAndTranscript
from livekit.plugins import noise_cancellation
from livekit.api import LiveKitAPI
from livekit import rtc
from agent.assistant import Assistant
from database.connection import DatabaseManager
from models import Customer
from shared_types.context_variables import ContextVariables
from config.settings import settings
from livekit import api
from helpers import redis_helper
from datetime import timedelta
from dotenv import load_dotenv


setup_logging()
logger = logging.getLogger(__name__)


class Database:
    """Main application class"""

    def __init__(self):
        self.db_manager = DatabaseManager()

    async def startup(self):
        """Application startup"""
        try:
            await self.db_manager.initialize()
            logger.info("Salon AI Application started successfully")
        except Exception as e:
            logger.error(f"Failed to start application: {e}")
            raise

    async def shutdown(self):
        """Application shutdown"""
        try:
            await self.db_manager.close()
            logger.info("Salon AI Application shutdown complete")
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")


async def delete_room(room_name: str):
    print("DELETING")
    lkapi = api.LiveKitAPI()
    await lkapi.room.delete_room(
        api.DeleteRoomRequest(
            room=room_name,
        )
    )


async def entrypoint(ctx: JobContext):
    try:
        db = Database()
        await db.startup()

        api = LiveKitAPI()
        rclient = redis.Redis(
            host = settings.REDIS_HOST, 
            port = settings.REDIS_PORT, 
            db=settings.REDIS_DB,
            decode_responses=True,
            username=settings.REDIS_USERNAME,
            password=settings.REDIS_PASS
        )

        context_variables: ContextVariables = {
            "appointment_id": uuid.uuid4(),
            "recording_id": uuid.uuid4(),
        }
        session = AgentSession()

        recording_and_transcript = RoomRecodingAndTranscript(
            ctx=ctx, session=session, ctx_vars=context_variables
        )

        await recording_and_transcript.make_egress_req()

        await ctx.connect()
        participant = await ctx.wait_for_participant()
        # await recording_and_transcript.turn_on_background_audio()
        context_variables["participant_id"] = participant.identity

        if participant.kind == rtc.ParticipantKind.PARTICIPANT_KIND_SIP:
            context_variables["phone_number"] = participant.attributes[
                "sip.phoneNumber"
            ]
        elif participant.kind == rtc.ParticipantKind.PARTICIPANT_KIND_STANDARD:
            context_variables["phone_number"] = "+14434231722"
        else:
            context_variables["phone_number"] = participant.attributes["phone_number"]

        if context_variables.get("phone_number"):
            customer_dict = rclient.hgetall(f'customer{context_variables["phone_number"]}') 
            if customer_dict:
                restored_data = redis_helper.restore_from_redis(customer_dict)
                customer = Customer()
                for key, value in restored_data.items():
                    setattr(customer, key, value)
                context_variables["customer"] = customer
            else:
                context_variables["customer"] = await Customer.find_by_phone(
                    phone_number=context_variables["phone_number"]
                )

            if context_variables["customer"]:
                customer_dict = redis_helper.prepare_for_redis(
                    context_variables["customer"].__dict__
                )

                key_name = f'customer{context_variables["phone_number"]}'
                rclient.hset(name=key_name, mapping=customer_dict)
                # rclient.expire(name=key_name, time=int(timedelta(minutes=30).total_seconds()))

        ctx.room.on(
            "participant_disconnected",
            lambda _: asyncio.create_task(delete_room(ctx.room.name)),
        )

        assistant = await Assistant.create(context_vars=context_variables, jobCtx=ctx)

        assistant.start_metrics_server()

        async def on_shutdown():
            await recording_and_transcript.write_transcript(
                is_appointment_created=assistant.is_appointment_created,
                customer_id=context_variables["customer"].id,
            )
            await assistant.log_usage_summary()
            await db.shutdown()

        ctx.add_shutdown_callback(on_shutdown)

        await session.start(
            agent=assistant,
            room=ctx.room,
            room_input_options=RoomInputOptions(
                noise_cancellation=noise_cancellation.BVCTelephony(),
            ),
        )

    except Exception as e:
        raise e


if __name__ == "__main__":
    load_dotenv()
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            shutdown_process_timeout=100,
            agent_name="inbound-agent",
        )
    )
