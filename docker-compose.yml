services:
  livekit-agent:
    build: .
    container_name: salon_livekit_agent
    restart: unless-stopped
    ports:
      - "8000:8000"  # Prometheus metrics port
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
    env_file:
      - .env
    volumes:
      - ./recordings:/app/recordings
      - ./logs:/app/logs
    networks:
      - salon_network

  prometheus:
    image: prom/prometheus:latest
    container_name: salon_prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - salon_network

  grafana:
    image: grafana/grafana:latest
    container_name: salon_grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning:ro
    networks:
      - salon_network
    depends_on:
      - prometheus

# Named volumes for data persistence
volumes:
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

# Custom network for service communication
networks:
  salon_network:
    driver: bridge